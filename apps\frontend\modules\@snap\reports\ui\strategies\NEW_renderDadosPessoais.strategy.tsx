import { ArrayRenderStrategy } from "./RenderStrategy";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { CustomGridItem } from "../components/CustomGridItem";
import { DadosPessoais } from "../../model/DadosPessoais";
import { ValueWithSource } from "../../model/ValueWithSource";
import { parseValue, translatePropToLabel } from "../../helpers";
import { useReportActions, useReportMode } from "../../context/ReportContext";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { renderSourceTooltip } from "./helpers.strategy";

export function useRenderDadosPessoais(sectionTitle: string): ArrayRenderStrategy<DadosPessoais> {
  const actions = useReportActions();
  const mode = useReportMode();

  const shouldInclude = (isDeleted: boolean) => {
    switch (mode) {
      case "trash":
        return isDeleted;
      case "print-pdf":
      case undefined:
      default:
        return !isDeleted;
    }
  };

  const testEntryDeleted = (entry: any): boolean => {
    return entry.detalhes
      ? Object.values(entry.detalhes).every((v: any) => v.is_deleted === true)
      : false;
  };

  const testSectionDeleted = (section: any): boolean =>
    Array.isArray(section.data) && section.data.every(testEntryDeleted);

  const calculateDataCount = (section: any): number => {
    if (!Array.isArray(section.data)) return 0;

    return section.data.reduce((count: number, entry: any) => {
      if (!entry.detalhes) return count;

      // contar itens não deletados
      const nonDeletedDetalhes = Object.values(entry.detalhes).filter(
        (item: any) => item.is_deleted !== true
      ).length;

      return count + nonDeletedDetalhes;
    }, 0);
  };

  const formatByKey: Record<
    string,
    (dadosPessoais?: DadosPessoais) => React.ReactElement | null
  > = {
    detalhes: (dadosPessoais) => {
      if (!dadosPessoais?.detalhes) return null;

      const entries = Object.entries(dadosPessoais.detalhes).filter(([, value]) => {
        const v = value as ValueWithSource<string> & { is_deleted: boolean };
        return shouldInclude(v.is_deleted);
      });

      if (entries.length === 0) return null;

      const onToggleField = (fieldKey: string) => {
        const updater = actions.updateSectionEntries;
        if (!updater) return;

        updater(
          sectionTitle,
          entry => {
            const det = (entry as any).detalhes?.[fieldKey];
            if (det) det.is_deleted = !det.is_deleted;
          },
          testEntryDeleted,
          testSectionDeleted,
          calculateDataCount
        );
      };

      return (
        <CustomGridContainer cols={3} columnFirst>
          {entries.map(([key, value], index) => {
            const valorComFonte = value as ValueWithSource<string>;
            return (
              <CustomGridItem key={`detalhes-${index}-${key}`} cols={1} onToggleField={() => onToggleField(key)}>
                <CustomReadOnlyInputField
                  label={`${(translatePropToLabel(valorComFonte.label)).toUpperCase()}`}
                  value={parseValue(String(valorComFonte.value))}
                  tooltip={renderSourceTooltip(valorComFonte.source)}
                />
              </CustomGridItem>
            );
          })}
        </CustomGridContainer>
      );
    },
  };

  const validateKeys = (keys: Array<keyof DadosPessoais>): boolean => {
    return keys.some((campo) => campo in formatByKey);
  };

  const renderSingleItem = (dadosPessoais: DadosPessoais): React.ReactElement[] => {
    const keys = Object.keys(dadosPessoais) as Array<keyof DadosPessoais>;

    if (!validateKeys(keys)) {
      console.warn("[Seção Dados Pessoais] Chaves inválidas:", keys);
    }

    return keys
      .map((chave) => formatByKey[chave]?.(dadosPessoais))
      .filter((el): el is React.ReactElement => el !== null);
  };

  const render = (dataArray: DadosPessoais[]): React.ReactElement[] => {
    if (!Array.isArray(dataArray)) {
      console.warn("[Seção Dados Pessoais] Expected array but received:", typeof dataArray);
      return [];
    }

    const filteredData = dataArray.filter((entry) => {
      if (mode === "trash") {
        return entry.detalhes && Object.values(entry.detalhes).some((v: any) => v.is_deleted === true);
      } else {
        const isDeleted = testEntryDeleted(entry);
        return !isDeleted;
      }
    });

    if (filteredData.length === 0) {
      return [];
    }

    const allElements: React.ReactElement[] = [];

    filteredData.forEach((dadosPessoais, index) => {
      const elements = renderSingleItem(dadosPessoais);

      if (filteredData.length > 1) {
        allElements.push(
          <div key={`dados-pessoais-${index}`} className="mb-4">
            {elements}
          </div>
        );
      } else {
        allElements.push(...elements);
      }
    });

    return allElements;
  };

  const deleteSectionEntries = () => {
    const updater = actions.updateSectionEntries;
    if (!updater) return;

    // No modo lixeira, restaura (is_deleted = false)
    // No modo normal, deleta (is_deleted = true)
    const targetDeletedState = mode !== "trash";

    updater(
      sectionTitle,
      entry => {
        // Percorre todos os detalhes e marca como deletado/restaurado
        if (entry.detalhes) {
          Object.values(entry.detalhes).forEach((detalhe: any) => {
            if (detalhe) detalhe.is_deleted = targetDeletedState;
          });
        }
      },
      testEntryDeleted,
      testSectionDeleted,
      calculateDataCount
    );
  };

  return {
    validateKeys,
    formatByKey,
    render,
    testEntryDeleted,
    testSectionDeleted,
    deleteSectionEntries,
  };
}
