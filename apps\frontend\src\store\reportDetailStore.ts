import { create } from "zustand";
import { REPORT_SECTIONS } from "~/helpers/constants";
import { ReportMetadata, ReportSection } from "~/types/global";
import { Draft, produce } from "immer";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { toast } from "sonner";
import { useCallback, useRef, useEffect } from "react";

type UpdateFnPromise = (entry: any, index?: number) => void;

interface ReportDetailStoreActions {
  setReportSections: (sections: ReportSection[]) => void;
  setDeletedSections: (sections: ReportSection[]) => void;
  setReportType: (type: string) => void;
  setMetadata: (metadata: ReportMetadata) => void;
  setProfileImage: (image: string) => void;
  updateSectionEntries: (
    sectionTitle: string,
    updaterFn: UpdateFnPromise,
    testEntryDeletedFn: (entry: ReportSection["data"][0]) => boolean,
    testSectionDeletedFn: (section: ReportSection) => boolean,
    calculateDataCountFn?: (section: ReportSection) => number
  ) => void;
}

interface ReportDetailStoreState {
  sections: ReportSection[];
  deletedSections: ReportSection[];
  metadata: ReportMetadata | null;
  reportType: string;
  profileImage: string | null;
  actions: ReportDetailStoreActions;
}

const useReportDetailStore = create<ReportDetailStoreState>((set) => ({
  sections: [],
  deletedSections: [],
  metadata: null,
  reportType: "",
  profileImage: null,
  actions: {
    setReportSections: (sections) => {
      const imagensSection = sections.find(
        (section) => section.title === REPORT_SECTIONS.imagens
      );
      if (imagensSection) {
        const firstImage = imagensSection.data[0].detalhes.find(
          (d: any) => d.value.url
        );
        if (firstImage) {
          set({ profileImage: firstImage.value.url.value });
        }
      } else {
        set({ profileImage: null });
      }
      set({ sections });
    },

    setDeletedSections: (deletedSections) => set({ deletedSections }),

    setMetadata: (metadata) => set({ metadata }),

    setReportType: (reportType) => set({ reportType }),

    setProfileImage: (profileImage) => set({ profileImage }),

    updateSectionEntries: (
      sectionTitle,
      updaterFn,
      testEntryDeletedFn,
      testSectionDeletedFn,
      calculateDataCountFn
    ) =>
      set((state) =>
        produce(state, (draft) => {
          const section = draft.sections.find(
            (s) => s.title === sectionTitle
          );
          if (!section) return;

          // 1) apply the mutation you passed, *with* its index
          section.data.forEach((entry, i) => updaterFn(entry as any, i))

          // 2) set the section flag however *you* want
          section.is_deleted = testSectionDeletedFn(section);

          // 3) update data_count if calculation function is provided
          if (calculateDataCountFn) {
            section.data_count = calculateDataCountFn(section);
          }
        })
      ),
  },
}));

export const useReportSections = () =>
  useReportDetailStore((state) => state.sections);
export const useReportDeletedSections = () =>
  useReportDetailStore((state) => state.deletedSections);
export const useReportMetadata = () =>
  useReportDetailStore((state) => state.metadata);
export const useReportType = () =>
  useReportDetailStore((state) => state.reportType);
export const useReportProfileImage = () =>
  useReportDetailStore((state) => state.profileImage);
export const useReportDetailActions = () =>
  useReportDetailStore((state) => state.actions);

export function useReportActionsWithAutoSave() {
  const actions = useReportDetailStore((state) => state.actions);
  const { addNewReportMutation, invalidateReportDetails } = useReportCRUD();

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pendingSaveRef = useRef<boolean>(false);
  const hasPendingChangesRef = useRef<boolean>(false);

  const DEBOUNCE_DELAY = 5000; // 5 segundos

  const executeSave = useCallback(() => {
    if (!hasPendingChangesRef.current || pendingSaveRef.current) {
      return;
    }

    const { sections, metadata } = useReportDetailStore.getState();
    const reportId = metadata?.user_reports_id as string;

    const payload = {
      ...metadata,
      data: { [metadata?.report_type as string]: sections },
    };

    console.log("[useReportActionsWithAutoSave] Executando save com estado final:", payload);
    pendingSaveRef.current = true;
    hasPendingChangesRef.current = false;

    addNewReportMutation.mutate(payload, {
      onSuccess: () => {
        pendingSaveRef.current = false;
        toast.success("Alterações salvas com sucesso!");
        if (reportId) invalidateReportDetails(reportId);
      },
      onError: (error) => {
        pendingSaveRef.current = false;
        hasPendingChangesRef.current = true;
        console.error("[AutoSave] Failed to save, invalidating anyway", error);
        toast.error("Falha ao salvar, tente novamente mais tarde.");
        if (reportId) invalidateReportDetails(reportId);
      },
    });
  }, [addNewReportMutation, invalidateReportDetails]);

  const scheduleAutoSave = useCallback(() => {
    hasPendingChangesRef.current = true;

    // Cancel previous mutation if it's still pending
    if (pendingSaveRef.current) {
      addNewReportMutation.reset();
      pendingSaveRef.current = false;
    }

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      executeSave();
    }, DEBOUNCE_DELAY);

    console.log("[useReportActionsWithAutoSave] Save agendado para", DEBOUNCE_DELAY / 1000, "segundos");
  }, [executeSave, DEBOUNCE_DELAY, addNewReportMutation]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const updateSectionEntries = useCallback((
    sectionTitle: string,
    updaterFn: UpdateFnPromise,
    testEntryDeletedFn: (entry: any) => boolean,
    testSectionDeletedFn: (section: any) => boolean,
    calculateDataCountFn?: (section: any) => number
  ) => {
    actions.updateSectionEntries(
      sectionTitle,
      updaterFn,
      testEntryDeletedFn,
      testSectionDeletedFn,
      calculateDataCountFn
    );

    scheduleAutoSave();
  }, [actions, scheduleAutoSave]);

  return {
    ...actions,
    updateSectionEntries,
    isPendingSave: () => pendingSaveRef.current,
    hasPendingChanges: () => hasPendingChangesRef.current,
    forceSave: executeSave,
    cancelAutoSave: () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      hasPendingChangesRef.current = false;
    },
  };
}